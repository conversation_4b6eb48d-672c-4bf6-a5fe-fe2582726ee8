import re
from typing import Dict, Any, Tuple
from enum import Enum


class ValidationType(Enum):
    """Enumeration for validation types"""
    REGEX = "REGEX"
    REGEX_LIST = "REGEX_LIST"
    EXPRESSION_TYPE = "EXPRESSION_TYPE"
    EXPRESSION_TYPE_LIST = "EXPRESSION_TYPE_LIST"


class ValidationUtils:
    """Utility class containing all validation logic for worksheet data"""

    def __init__(self):
        pass

    def validate_field(self, field_value: Any, field_config: Dict, config: Dict, all_data: Dict = None, current_group: str = None, current_field: str = None) -> list:
        """Main field validation method that routes to specific validation types"""
        field_errors = []

        if field_value == "N/A":
            field_value = ""

        if field_config.get("required", False) and (field_value is None or field_value == ""):
            field_errors.append("Field is required")

        if not field_config.get("required", False) and (field_value is None or field_value == ""):
            return field_errors

        validation_rules = field_config.get("validation_rules", [])
        for rule in validation_rules:
            validation_type = rule.get("isValidationType")

            try:
                validation_enum = ValidationType(validation_type)
            except ValueError:
                field_errors.append(f"Unsupported validation type: {validation_type}. Supported types: {[vt.value for vt in ValidationType]}")
                continue

            match validation_enum:
                case ValidationType.REGEX:
                    is_valid, error_msg = self.validate_regex(field_value, rule)
                    if not is_valid:
                        field_errors.append(error_msg)
                        break

                case ValidationType.REGEX_LIST:
                    is_valid, error_msg = self.validate_regex_list(field_value, rule)
                    if not is_valid:
                        field_errors.append(error_msg)
                        break

                case ValidationType.EXPRESSION_TYPE:
                    is_valid, error_msg = self.validate_expression(field_value, rule, config, all_data, current_group, current_field)
                    if not is_valid:
                        field_errors.append(error_msg)
                        break

                case ValidationType.EXPRESSION_TYPE_LIST:
                    is_valid, error_msg = self.validate_expression_list(field_value, rule, config, all_data, current_group, current_field)
                    if not is_valid:
                        field_errors.append(error_msg)
                        break

        return field_errors

    def validate_expression(self, field_value: Any, rule: Dict, config: Dict, all_data: Dict = None, current_group: str = None, current_field: str = None) -> Tuple[bool, str]:
        """Validate using single direct expression"""
        try:
            expression = rule.get("expression", "")
            error_msg = rule.get("error_msg", "Expression validation failed")

            if not expression:
                return False, "Expression is required for EXPRESSION_TYPE validation"

            result = self._evaluate_direct_expression(expression, field_value, all_data, current_group, current_field, config)
            return result, "" if result else error_msg

        except Exception as e:
            return False, f"Expression validation error: {str(e)}"

    def validate_expression_list(self, field_value: Any, rule: Dict, config: Dict, all_data: Dict = None, current_group: str = None, current_field: str = None) -> Tuple[bool, str]:
        """Validate using list of expressions with individual error messages"""
        try:
            expressions = rule.get("expressions", [])
            error_msgs = rule.get("error_msgs", ["Expression validation failed"])
            condition_type = rule.get("conditionType", "AND")

            if not expressions:
                return False, "Expressions list is required for EXPRESSION_TYPE_LIST validation"

            # Evaluate all expressions
            results = []
            for i, expr in enumerate(expressions):
                result = self._evaluate_direct_expression(expr, field_value, all_data, current_group, current_field, config)
                results.append(result)

                # If this expression failed and we have a specific error message for it
                if not result and i < len(error_msgs):
                    return False, error_msgs[i]

            # Apply condition type logic
            if condition_type.upper() == "AND":
                final_result = all(results)
            elif condition_type.upper() == "OR":
                final_result = any(results)
            else:
                final_result = results[0] if results else False

            # Return appropriate error message
            if not final_result:
                if len(error_msgs) > 0:
                    return False, error_msgs[0]  # Use first error message as default
                else:
                    return False, "Expression validation failed"

            return True, ""

        except Exception as e:
            return False, f"Expression validation error: {str(e)}"

    def _evaluate_direct_expression(self, expression: str, field_value: Any, all_data: Dict, current_group: str, current_field: str, config: Dict) -> bool:
        """Evaluate direct JavaScript-like expressions with field references"""
        try:
            # Replace field references with actual values
            processed_expression = self._replace_field_references(expression, field_value, all_data, current_group, current_field)

            # Execute the processed expression
            return self._execute_direct_expression(processed_expression)

        except Exception as e:
            print(f"Error evaluating direct expression '{expression}': {str(e)}")
            return False

    def _replace_field_references(self, expression: str, field_value: Any, all_data: Dict, current_group: str, current_field: str) -> str:
        """Replace field references like 'mv1.value', 'bos.buyer_name' with actual values"""
        import re

        processed_expression = expression

        # Find and replace group.field references first (more specific)
        field_pattern = r'\b([a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\b'
        field_refs = re.findall(field_pattern, expression)

        for field_ref in field_refs:
            actual_value = self._get_field_value_by_reference(field_ref, all_data, current_group)
            formatted_value = self._format_value_for_direct_expression(actual_value)
            processed_expression = processed_expression.replace(field_ref, formatted_value)

        # Replace standalone 'value' with current field value (only if not already replaced as part of a field reference)
        if re.search(r'\bvalue\b', processed_expression):
            processed_expression = re.sub(r'\bvalue\b', self._format_value_for_direct_expression(field_value), processed_expression)

        return processed_expression

    def _get_field_value_by_reference(self, field_ref: str, all_data: Dict, current_group: str) -> Any:
        """Get field value by reference like 'mv1.year' or 'bos.buyer_name.value'"""
        if not all_data or "groups" not in all_data:
            return None

        parts = field_ref.split('.')
        if len(parts) < 2:
            return None

        group_name = parts[0]
        field_path = parts[1:]

        groups = all_data["groups"]
        if group_name not in groups:
            return None

        # Navigate through the field path
        current_data = groups[group_name].get("fields", {})

        for part in field_path:
            if isinstance(current_data, dict) and part in current_data:
                current_data = current_data[part]
            else:
                return None

        return current_data

    def _format_value_for_direct_expression(self, value: Any) -> str:
        """Format a value for use in direct expressions"""
        if value is None or value == "":
            return "None"
        elif isinstance(value, str):
            # Escape quotes and wrap in quotes
            escaped_value = value.replace("'", "\\'").replace('"', '\\"')
            return f'"{escaped_value}"'
        elif isinstance(value, bool):
            return "True" if value else "False"
        else:
            return str(value)

    def _execute_direct_expression(self, expression: str) -> bool:
        """Execute direct JavaScript-like expression safely"""
        try:
            # Preprocess for null safety in mathematical operations
            processed_expression = self._preprocess_expression_for_null_safety(expression)

            # Convert JavaScript operators and values to Python
            python_expression = processed_expression.replace('&&', ' and ').replace('||', ' or ')
            python_expression = python_expression.replace('null', 'None')
            python_expression = python_expression.replace('true', 'True').replace('false', 'False')

            # Safe evaluation with essential functions only
            allowed_names = {
                "__builtins__": {},
                "True": True,
                "False": False,
                "None": None,
                "abs": self._safe_abs,
                "safe_subtract": self._safe_subtract,
                "max": max,
                "min": min
            }

            result = eval(python_expression, allowed_names, {})
            return bool(result)

        except Exception as e:
            print(f"Error executing direct expression '{expression}': {str(e)}")
            return False

    def _safe_abs(self, value):
        """Safe absolute value function that handles None values"""
        if value is None:
            return 0
        try:
            return abs(float(value))
        except (ValueError, TypeError):
            return 0

    def _safe_subtract(self, a, b):
        """Safe subtraction that handles None values"""
        if a is None or b is None:
            return 0
        try:
            return float(a) - float(b)
        except (ValueError, TypeError):
            return 0

    def _preprocess_expression_for_null_safety(self, expression: str) -> str:
        """Preprocess expression to handle null values in mathematical operations"""
        import re

        # Replace abs(a - b) patterns with safe_abs(safe_subtract(a, b))
        abs_pattern = r'abs\(([^)]+)\s*-\s*([^)]+)\)'

        def replace_abs_subtract(match):
            left = match.group(1).strip()
            right = match.group(2).strip()
            return f'abs(safe_subtract({left}, {right}))'

        processed = re.sub(abs_pattern, replace_abs_subtract, expression)
        return processed





    def validate_regex(self, value: Any, rule: Dict) -> Tuple[bool, str]:
        """Validate value against single regex pattern"""
        try:
            regex_pattern = rule.get("regex", "")
            error_msg = rule.get("error_msg", "Value does not match required pattern")

            if not regex_pattern:
                return False, "Regex pattern not specified"

            str_value = str(value) if value is not None else ""

            if re.match(regex_pattern, str_value):
                return True, ""
            else:
                return False, error_msg

        except Exception as e:
            return False, f"Regex validation error: {str(e)}"

    def validate_regex_list(self, value: Any, rule: Dict) -> Tuple[bool, str]:
        """Validate value against list of regex patterns with individual error messages"""
        try:
            regex_patterns = rule.get("regexes", [])
            error_msgs = rule.get("error_msgs", ["Value does not match required pattern"])
            condition_type = rule.get("conditionType", "AND")

            if not regex_patterns:
                return False, "Regex patterns list not specified"

            str_value = str(value) if value is not None else ""

            # Test all patterns
            results = []
            for i, pattern in enumerate(regex_patterns):
                match_result = bool(re.match(pattern, str_value))
                results.append(match_result)

                # If this pattern failed and we have a specific error message for it
                if not match_result and condition_type.upper() == "AND" and i < len(error_msgs):
                    return False, error_msgs[i]

            # Apply condition type logic
            if condition_type.upper() == "AND":
                final_result = all(results)
            elif condition_type.upper() == "OR":
                final_result = any(results)
            else:
                final_result = results[0] if results else False

            if final_result:
                return True, ""
            else:
                # Return appropriate error message
                if len(error_msgs) > 0:
                    return False, error_msgs[0]  # Use first error message as default
                else:
                    return False, "Value does not match required pattern"

        except Exception as e:
            return False, f"Regex validation error: {str(e)}"
